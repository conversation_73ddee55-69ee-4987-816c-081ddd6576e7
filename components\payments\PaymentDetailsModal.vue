<template>
  <l-dialog
    :dialog="show"
    max-width="800"
    custom-class="payment-details-modal"
    @close-dialog="$emit('close')"
  >
    <v-card class="pa-2" flat>
      <div class="d-flex justify-space-between align-center mb-2">
        <h2 class="text-h6 font-weight-medium">{{ title }}</h2>
      </div>

      <v-form ref="form" @submit.prevent="handleSubmit">
        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Account Owner Name:</div>
            <text-input
              :value="form.accountOwnerName"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.maxLength255]"
              @input="form.accountOwnerName = $event"
            />
          </v-col>
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Email:</div>
            <text-input
              :value="form.email"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.email, rules.maxLength255]"
              @input="form.email = $event"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Phone Number:</div>
            <text-input
              :value="form.phoneNumber"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.phoneNumber]"
              @input="form.phoneNumber = $event"
            />
          </v-col>
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">{{ accountNumberLabel }}</div>
            <text-input
              :value="form.iban"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, accountNumber]"
              @input="form.iban = $event"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">{{ routingNumberLabel }}</div>
            <text-input
              :value="form.bic"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, routingNumber]"
              @input="form.bic = $event"
            />
          </v-col>
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Address Line 1:</div>
            <text-input
              :value="form.addressLine1"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.maxLength255]"
              @input="form.addressLine1 = $event"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Address Line 2:</div>
            <text-input
              :value="form.addressLine2"
              type-class="border-gradient"
              height="44"
              :rules="[rules.maxLength255]"
              @input="form.addressLine2 = $event"
            />
          </v-col>
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">City:</div>
            <text-input
              :value="form.city"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.maxLength255]"
              @input="form.city = $event"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Region:</div>
            <text-input
              :value="form.region"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.maxLength255]"
              @input="form.region = $event"
            />
          </v-col>
          <v-col cols="12" sm="6" class="form-col country-select">
            <div class="input-label mb-1">Country:</div>
            <SelectInput
              v-model="form.country"
              height="44"
              :items="countries"
              item-value="code"
              item-name="name"
              :menu-props="{ maxHeight: 300, minWidth: 250 }"
              :hide-item-icon="false"
              :rules="[rules.required, rules.countryCode]"
            />
            <!-- <text-input
              :value="form.country"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required]"
              @input="form.country = $event"
            /> -->
          </v-col>
        </v-row>

        <v-row no-gutters class="form-row">
          <v-col cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Postal Code:</div>
            <text-input
              :value="form.postalCode"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.postalCode]"
              @input="form.postalCode = $event"
            />
          </v-col>
          <v-col v-if="showCurrency" cols="12" sm="6" class="form-col">
            <div class="input-label mb-1">Currency:</div>
            <text-input
              :value="form.currency"
              type-class="border-gradient"
              height="44"
              :rules="[rules.required, rules.currencyCode]"
              @input="form.currency = $event"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="mb-2">
          <v-col cols="12">
            <v-checkbox
              v-model="form.saveThisAccount"
              label="Save this account for future payouts"
              class="mt-0 pt-0 payment-detail-modal"
              dense
              hide-details
            />
          </v-col>
        </v-row>

        <div class="d-flex justify-end form-actions">
          <v-btn
            color="primary"
            large
            class="px-12"
            type="submit"
            :loading="loading"
          >
            Confirm payout
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </l-dialog>
</template>

<script>
import { countries } from './countries'
import LDialog from '~/components/LDialog'
import TextInput from '~/components/form/TextInput'
import SelectInput from '~/components/form/SelectInput'
export default {
  name: 'PaymentDetailsModal',
  components: {
    LDialog,
    TextInput,
    SelectInput,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    paymentType: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      form: {
        accountOwnerName: '',
        email: '',
        phoneNumber: '',
        iban: '',
        bic: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        region: '',
        country: '',
        postalCode: '',
        currency: '',
        typeBank: '',
        saveThisAccount: false,
      },
      countries,
      rules: {
        required: (v) => !!v || 'This field is required',
        email: (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
        maxLength255: (v) =>
          !v || v.length <= 255 || 'Maximum 255 characters allowed',

        phoneNumber: (v) =>
          !v ||
          /^\+[0-9]{10,15}$/.test(v) ||
          'Phone number must be in international format (+ followed by 10-15 digits)',
        postalCode: (v) =>
          !v ||
          /^[A-Za-z0-9 -]{4,10}$/.test(v) ||
          'Postal code must be 4-10 characters (letters, numbers, spaces, and dashes allowed)',
        countryCode: (v) =>
          !v ||
          /^[A-Z]{2}$/.test(v) ||
          'Please enter a valid ISO country code (e.g., GB, PL, DE)',
        currencyCode: (v) =>
          !v ||
          /^[A-Z]{3}$/.test(v) ||
          'Please enter a valid 3-letter currency code',
        typeBank: (v) =>
          !v || v.length <= 255 || 'Maximum 255 characters allowed',
      },
    }
  },
  computed: {
    title() {
      // Remove "Details" from specific payment types
      if (
        this.paymentType === 'Transfer to UK Account' ||
        this.paymentType === 'Transfer to US Account' ||
        this.paymentType === 'SWIFT Transfer'
      ) {
        return this.paymentType
      }
      return `${this.paymentType} Details`
    },
    showCurrency() {
      // Hide currency field for specific payment types
      return !(
        this.paymentType === 'IBAN Transfer' ||
        this.paymentType === 'Transfer to UK Account' ||
        this.paymentType === 'Transfer to US Account' ||
        this.paymentType === 'SWIFT Transfer'
      )
    },
    accountNumberLabel() {
      // Change label for IBAN Transfer
      if (this.paymentType === 'IBAN Transfer') {
        return 'IBAN:'
      }
      return 'Account number:'
    },
    routingNumberLabel() {
      // Change label based on payment type
      if (this.paymentType === 'Transfer to UK Account') {
        return 'Sort code:'
      } else if (this.paymentType === 'SWIFT Transfer') {
        return 'BIC/SWIFT Number:'
      }
      return 'BIC:'
    },
    // Dynamic validation rules based on payment type
    accountNumber() {
      if (this.paymentType === 'Transfer to UK Account') {
        // UK account number: exactly 8 digits
        return (v) =>
          !v ||
          /^[0-9]{8}$/.test(v) ||
          'UK account number must be exactly 8 digits'
      } else if (this.paymentType === 'IBAN Transfer') {
        // IBAN validation
        return (v) =>
          !v ||
          /^[A-Z]{2}[0-9A-Z]{2,30}$/.test(v) ||
          'Please enter a valid IBAN'
      } else {
        // Default account number validation for US and other accounts
        return (v) =>
          !v ||
          /^[0-9A-Z]{4,20}$/.test(v) ||
          'Account number must be 4-20 alphanumeric characters'
      }
    },
    routingNumber() {
      if (this.paymentType === 'Transfer to UK Account') {
        // UK sort code: exactly 6 digits
        return (v) =>
          !v || /^[0-9]{6}$/.test(v) || 'UK sort code must be exactly 6 digits'
      } else if (this.paymentType === 'SWIFT Transfer') {
        // BIC/SWIFT validation
        return (v) =>
          !v ||
          /^[A-Z0-9]{8,11}$/.test(v) ||
          'BIC/SWIFT number must be 8-11 alphanumeric characters'
      } else {
        // Default BIC validation for IBAN and other transfers
        return (v) =>
          !v ||
          /^[A-Z0-9]{8,11}$/.test(v) ||
          'This field must be 8-11 alphanumeric characters'
      }
    },
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm()
      }
    },
  },
  methods: {
    resetForm() {
      this.form = {
        accountOwnerName: '',
        email: '',
        phoneNumber: '',
        iban: '',
        bic: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        region: '',
        country: '',
        postalCode: '',
        currency: this.showCurrency ? '' : null, // Set to null when currency is not shown
        typeBank: this.getTypeBankFromPaymentType(),
        saveThisAccount: false,
      }
      if (this.$refs.form) {
        this.$refs.form.resetValidation()
      }
    },

    getTypeBankFromPaymentType() {
      // Map payment type to typeBank value
      switch (this.paymentType) {
        case 'IBAN Transfer':
          return 'iban'
        case 'Transfer to UK Account':
          return 'uk'
        case 'Transfer to US Account':
          return 'us'
        case 'SWIFT Transfer':
          return 'swift'
        default:
          return ''
      }
    },
    async handleSubmit() {
      // Validate the form
      const isValid = this.$refs.form.validate()

      if (!this.loading && isValid) {
        this.loading = true
        try {
          // Ensure currency is null when not shown
          if (!this.showCurrency) {
            this.form.currency = null
          }

          // Prepare the payload with the correct field names and ensure all required fields are included
          const payload = {
            accountOwnerName: this.form.accountOwnerName || '',
            email: this.form.email || '',
            phoneNumber: this.form.phoneNumber || '',
            iban: this.form.iban || '',
            bic: this.form.bic || '',
            addressLine1: this.form.addressLine1 || '',
            addressLine2: this.form.addressLine2 || '',
            city: this.form.city || '',
            region: this.form.region || '',
            country: this.form.country?.code || '',
            postalCode: this.form.postalCode || '',
            currency: this.showCurrency ? this.form.currency || '' : null,
            typeBank: this.form.typeBank || this.getTypeBankFromPaymentType(),
            saveThisAccount: this.form.saveThisAccount || false,
          }

          // Call the bank payout API
          const result = await this.$store.dispatch(
            'payments/requestBankPayout',
            payload
          )

          if (result.success) {
            this.$store.dispatch(
              'snackbar/success',
              { successMessage: 'Form submitted successfully' },
              { root: true }
            )
            this.$emit('submit', this.form)
            this.$emit('close')
          } else {
            this.$store.dispatch(
              'snackbar/error',
              { errorMessage: result.message || 'Something went wrong' },
              { root: true }
            )
          }
        } catch (error) {
          // Show generic error message if the store action throws
          this.$store.dispatch(
            'snackbar/error',
            { errorMessage: 'Something went wrong' },
            { root: true }
          )
        } finally {
          this.loading = false
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.payment-details-modal {
  .v-card {
    padding: 24px;
  }
  .v-text-field .v-input .v-input__control .v-text-field--outlined {
    border-radius: 16px !important;
  }
  .form-row {
    margin-bottom: 16px;
  }

  .form-col {
    padding: 0 8px;
  }
  .country-select .l-select {
    border: 1px solid !important;
    border-radius: 24px !important;
    color: rgba(0, 0, 0, 0.3);
  }
  .country-select ::v-deep .v-select__selections {
    margin-left: 8px;
  }

  .input-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }

  @media (max-width: 599px) {
    .v-card {
      padding: 16px;
    }

    .form-row {
      margin-bottom: 0;
    }

    .form-col {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 4px;
      }
    }

    .form-actions {
      flex-direction: column;
      margin-top: 24px;

      .v-btn {
        width: max-content;
        margin-left: 0 !important;
      }
    }

    .v-checkbox {
      margin-top: 8px !important;
    }
  }
}
</style>
